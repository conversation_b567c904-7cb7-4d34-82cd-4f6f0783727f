<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Verifikasi Pendaftaran" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <Heading title="Verifikasi Pendaftaran" />
        <div class="flex items-center space-x-3">
          <Button
            v-if="selectedRegistrations.length > 0"
            @click="showBulkVerifyModal = true"
            variant="outline"
            class="text-green-600 border-green-600 hover:bg-green-50"
          >
            <Icon name="check" class="w-4 h-4 mr-2" />
            Verifikasi Massal ({{ selectedRegistrations.length }})
          </Button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-blue-500 rounded-full">
                <Icon name="fileText" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-blue-700">{{ stats.total_registrations }}</p>
                <p class="text-sm text-blue-600">Total Pendaftaran</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-yellow-500 rounded-full">
                <Icon name="clock" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-yellow-700">{{ stats.pending_verification }}</p>
                <p class="text-sm text-yellow-600">Menunggu Verifikasi</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-green-500 rounded-full">
                <Icon name="checkCircle" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-green-700">{{ stats.verified }}</p>
                <p class="text-sm text-green-600">Terverifikasi</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-red-500 rounded-full">
                <Icon name="xCircle" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-red-700">{{ stats.rejected }}</p>
                <p class="text-sm text-red-600">Ditolak</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Filters and Search -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- Search -->
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="searchForm.search"
                placeholder="Nama, NIK, No. Pendaftaran..."
                @input="debouncedSearch"
              />
            </div>

            <!-- Status Filter -->
            <div>
              <Label for="status">Status</Label>
              <Select v-model="searchForm.status" @update:model-value="applyFilters">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Status</SelectItem>
                  <SelectItem
                    v-for="status in filterOptions.statuses"
                    :key="status.value"
                    :value="status.value"
                  >
                    {{ status.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Wilayah Filter -->
            <div>
              <Label for="wilayah">Wilayah</Label>
              <Select v-model="searchForm.wilayah" @update:model-value="applyFilters">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Wilayah" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Wilayah</SelectItem>
                  <SelectItem
                    v-for="wilayah in filterOptions.wilayah"
                    :key="wilayah.id_wilayah"
                    :value="wilayah.id_wilayah.toString()"
                  >
                    {{ wilayah.nama_wilayah }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Cabang Lomba Filter -->
            <div>
              <Label for="cabang_lomba">Cabang Lomba</Label>
              <Select v-model="searchForm.cabang_lomba" @update:model-value="applyFilters">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Cabang" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Cabang</SelectItem>
                  <SelectItem
                    v-for="cabang in filterOptions.cabangLomba"
                    :key="cabang.id_cabang"
                    :value="cabang.id_cabang.toString()"
                  >
                    {{ cabang.nama_cabang }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Reset Filters -->
            <div class="flex items-end">
              <Button @click="resetFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Registrations Table -->
      <Card class="islamic-shadow">
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left">
                    <Checkbox
                      :checked="allSelected"
                      @update:checked="toggleSelectAll"
                    />
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Peserta
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pendaftaran
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Lomba
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="registration in registrations.data" :key="registration.id_pendaftaran" class="hover:bg-gray-50">
                  <td class="px-6 py-4">
                    <Checkbox
                      :checked="selectedRegistrations.includes(registration.id_pendaftaran)"
                      @update:checked="toggleRegistrationSelection(registration.id_pendaftaran)"
                    />
                  </td>
                  <td class="px-6 py-4">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ registration.peserta.nama_lengkap }}
                      </div>
                      <div class="text-sm text-gray-500">
                        NIK: {{ registration.peserta.nik }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ registration.peserta.wilayah?.nama_wilayah }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ registration.nomor_pendaftaran }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ formatDate(registration.created_at) }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ registration.golongan.nama_golongan }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ registration.golongan.cabang_lomba.nama_cabang }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <Badge :variant="getStatusVariant(registration.status_pendaftaran)">
                      {{ getStatusLabel(registration.status_pendaftaran) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4">
                    <div class="space-y-2">
                      <!-- Overall Progress -->
                      <div class="flex items-center space-x-2">
                        <div class="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            :style="{ width: `${registration.verification_progress.overall_percentage}%` }"
                          ></div>
                        </div>
                        <span class="text-xs text-gray-500">
                          {{ registration.verification_progress.overall_percentage }}%
                        </span>
                      </div>
                      <!-- Document Status -->
                      <div class="flex items-center space-x-1 text-xs">
                        <Icon name="file" class="w-3 h-3 text-gray-400" />
                        <span class="text-green-600">{{ registration.document_status.approved }}</span>
                        <span class="text-gray-400">/</span>
                        <span class="text-gray-600">{{ registration.document_status.total }}</span>
                        <span class="text-gray-400">docs</span>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="flex items-center space-x-2">
                      <Button
                        @click="viewRegistration(registration)"
                        size="sm"
                        variant="outline"
                      >
                        <Icon name="eye" class="w-4 h-4" />
                      </Button>
                      <Button
                        v-if="canVerify(registration)"
                        @click="quickVerify(registration, 'approve')"
                        size="sm"
                        class="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <Icon name="check" class="w-4 h-4" />
                      </Button>
                      <Button
                        v-if="canVerify(registration)"
                        @click="quickVerify(registration, 'reject')"
                        size="sm"
                        variant="destructive"
                      >
                        <Icon name="x" class="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="px-6 py-4 border-t">
            <Pagination
              :links="registrations.links"
              :from="registrations.from"
              :to="registrations.to"
              :total="registrations.total"
            />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Bulk Verify Modal -->
    <BulkVerifyModal
      v-model:show="showBulkVerifyModal"
      :selected-count="selectedRegistrations.length"
      @verify="handleBulkVerify"
    />
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import { ref, computed, watch } from 'vue'
import { debounce } from 'lodash'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import BulkVerifyModal from './BulkVerifyModal.vue'
import { type BreadcrumbItem } from '@/types'

// Props
interface Props {
  registrations: {
    data: any[]
    links: any[]
    from: number
    to: number
    total: number
  }
  filters: {
    status?: string
    wilayah?: string
    cabang_lomba?: string
    golongan?: string
    search?: string
  }
  filterOptions: {
    statuses: Array<{ value: string; label: string }>
    wilayah: Array<{ id_wilayah: number; nama_wilayah: string }>
    cabangLomba: Array<{ id_cabang: number; nama_cabang: string }>
    golongan: Array<{ id_golongan: number; nama_golongan: string }>
  }
  stats: {
    total_registrations: number
    pending_verification: number
    verified: number
    approved: number
    rejected: number
  }
}

const props = defineProps<Props>()

// Breadcrumbs
const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard Admin', href: '/admin/dashboard' },
  { title: 'Verifikasi Pendaftaran', href: '/admin/registration-verification' }
]

// Reactive data
const selectedRegistrations = ref<number[]>([])
const showBulkVerifyModal = ref(false)
const searchForm = ref({
  search: props.filters.search || '',
  status: props.filters.status || '',
  wilayah: props.filters.wilayah || '',
  cabang_lomba: props.filters.cabang_lomba || '',
  golongan: props.filters.golongan || ''
})

// Computed
const allSelected = computed(() => {
  return props.registrations.data.length > 0 &&
         selectedRegistrations.value.length === props.registrations.data.length
})

// Methods
const toggleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedRegistrations.value = props.registrations.data.map(r => r.id_pendaftaran)
  } else {
    selectedRegistrations.value = []
  }
}

const toggleRegistrationSelection = (id: number) => {
  const index = selectedRegistrations.value.indexOf(id)
  if (index > -1) {
    selectedRegistrations.value.splice(index, 1)
  } else {
    selectedRegistrations.value.push(id)
  }
}

const debouncedSearch = debounce(() => {
  applyFilters()
}, 500)

const applyFilters = () => {
  router.get('/admin/registration-verification', searchForm.value, {
    preserveState: true,
    preserveScroll: true
  })
}

const resetFilters = () => {
  searchForm.value = {
    search: '',
    status: '',
    wilayah: '',
    cabang_lomba: '',
    golongan: ''
  }
  applyFilters()
}

const viewRegistration = (registration: any) => {
  router.visit(`/admin/registration-verification/${registration.id_pendaftaran}`)
}

const canVerify = (registration: any) => {
  return ['submitted', 'paid'].includes(registration.status_pendaftaran)
}

const quickVerify = (registration: any, action: 'approve' | 'reject') => {
  const confirmed = confirm(
    `Apakah Anda yakin ingin ${action === 'approve' ? 'menyetujui' : 'menolak'} pendaftaran ini?`
  )

  if (confirmed) {
    router.post(`/admin/registration-verification/${registration.id_pendaftaran}/verify`, {
      action,
      notes: ''
    })
  }
}

const handleBulkVerify = (data: { action: string; notes: string }) => {
  router.post('/admin/registration-verification/bulk-verify', {
    registration_ids: selectedRegistrations.value,
    action: data.action,
    notes: data.notes
  }, {
    onSuccess: () => {
      selectedRegistrations.value = []
      showBulkVerifyModal.value = false
    }
  })
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'draft': 'secondary',
    'submitted': 'default',
    'payment_pending': 'outline',
    'paid': 'default',
    'verified': 'default',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'draft': 'Draft',
    'submitted': 'Submitted',
    'payment_pending': 'Menunggu Pembayaran',
    'paid': 'Dibayar',
    'verified': 'Terverifikasi',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
